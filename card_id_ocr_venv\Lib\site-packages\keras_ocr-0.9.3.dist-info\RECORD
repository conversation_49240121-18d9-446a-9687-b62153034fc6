keras_ocr-0.9.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
keras_ocr-0.9.3.dist-info/LICENSE,sha256=wYHu0lSmCDmaEFnd9ctyyx8aWEGgg6qjDz5nE4U9358,1022
keras_ocr-0.9.3.dist-info/METADATA,sha256=AL8Fc_l6on8SHdJH6TOxLeEHWcqa88Q8r0oOlQzNA4s,8568
keras_ocr-0.9.3.dist-info/RECORD,,
keras_ocr-0.9.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
keras_ocr-0.9.3.dist-info/WHEEL,sha256=FMvqSimYX_P7y0a7UY-_Mc83r5zkBZsCYPm7Lr0Bsq4,88
keras_ocr/__init__.py,sha256=--PXdgkbdCNTj5Npdrg5ApY0FNyUVmWpbXIwiMbdSoU,161
keras_ocr/__pycache__/__init__.cpython-313.pyc,,
keras_ocr/__pycache__/config.cpython-313.pyc,,
keras_ocr/__pycache__/data_generation.cpython-313.pyc,,
keras_ocr/__pycache__/datasets.cpython-313.pyc,,
keras_ocr/__pycache__/detection.cpython-313.pyc,,
keras_ocr/__pycache__/evaluation.cpython-313.pyc,,
keras_ocr/__pycache__/pipeline.cpython-313.pyc,,
keras_ocr/__pycache__/recognition.cpython-313.pyc,,
keras_ocr/__pycache__/tools.cpython-313.pyc,,
keras_ocr/config.py,sha256=ET1enzVgCrXYMQA-iiPzSM643Asflhm5CuE0rRIRJbE,867
keras_ocr/data_generation.py,sha256=dlW3Mbf8IGSwkhnOn-E-2VLbhhckdjGrBBOhPRh6VuM,26309
keras_ocr/datasets.py,sha256=B0rFe-LJ1nmvL8poUd-_cePboGOOk0-Hw82rx119vAc,19052
keras_ocr/detection.py,sha256=7ewbDqSgiuGjKCxM8z2bmctQnpZrCgWLNcln7Viit8g,30332
keras_ocr/evaluation.py,sha256=QzXLzzSnJA2PYNZrsGdVxSwEmVjYR4hcIMsLteJFD60,6354
keras_ocr/pipeline.py,sha256=8zNmgOCGZRFbSZInTWTtnu1e4wgEPH3OFi_2htM-nTY,2722
keras_ocr/recognition.py,sha256=NlawzVIHpneLhjjb0dxFf9piK99d0IoEj2bBT53dBpk,21180
keras_ocr/tools.py,sha256=kJ5in7H78WWeqUpCpIhh2xFUQzvyWi-fw0sKhvRQOgo,20719
